# QA Pipeline Web Console - Troubleshooting Guide

## Issues Fixed in This Session

### ✅ 1. Missing Template Errors
**Problem**: TemplateNotFound exceptions for several admin templates
**Solution**: Created all missing templates:
- `templates/dashboard/system_info.html` - System information and connection status
- `templates/admin/index.html` - Admin dashboard overview
- `templates/admin/users.html` - User management interface
- `templates/admin/system.html` - System configuration page
- `templates/admin/edit_user.html` - User editing form

### ✅ 2. SQLAlchemy Case() Syntax Error
**Problem**: `sqlalchemy.exc.ArgumentError` with deprecated case() syntax
**Solution**: Updated SQLAlchemy queries in `web_console/admin.py`:
```python
# OLD (deprecated)
db.case([(condition, value)], else_=default)

# NEW (correct)
db.case((condition, value), else_=default)
```

### ✅ 3. SQLAlchemy ORDER BY Error
**Problem**: `sqlalchemy.exc.CompileError` with textual ORDER BY expressions
**Solution**: Wrapped ORDER BY strings with `db.text()`:
```python
# OLD (causes error)
.order_by('total_processed desc')

# NEW (correct)
.order_by(db.text('total_processed desc'))
```

### ✅ 4. CSRF Token Missing Error
**Problem**: Forms missing CSRF tokens causing "Bad Request" errors
**Solution**: Added CSRF tokens to all forms in templates:
```html
<input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
```

### ✅ 5. Pinecone Connection Error
**Problem**: "Pinecone client not properly initialized" error
**Solution**: 
1. Installed pinecone-client package: `pip install pinecone-client`
2. Updated `utils.py` with proper Pinecone initialization and error handling

### ✅ 6. MySQL Connection Error
**Problem**: SQL syntax errors with database queries
**Solution**: Updated connection test to use `db.text()` for raw SQL queries and handle both MySQL and SQLite

## Connection Status Indicators

The web console now provides real-time connection status for all services:

### 🟢 Connected (Green)
- Service is properly configured and responding
- All functionality available

### 🟡 Warning (Yellow) 
- Service is not configured (missing environment variables)
- Partial functionality or configuration needed

### 🔴 Error (Red)
- Service connection failed
- Check configuration and network connectivity

## Environment Variables Required

### MongoDB Configuration
```bash
MONGO_URI=mongodb://localhost:27017/
MONGO_DB_NAME=your_database_name
MONGO_COLLECTION_NAME=your_collection_name
```

### Pinecone Configuration
```bash
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX_NAME=your_index_name
```

### OpenAI Configuration
```bash
OPENAI_API_KEY=your_openai_api_key
OPENAI_ASSISTANT_ID=your_assistant_id
```

### Web Console Database
```bash
# For SQLite (development)
DATABASE_URL=sqlite:///qa_pipeline.db

# For MySQL (production)
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/qa_pipeline_db
```

## Common Issues and Solutions

### Issue: Pinecone Shows "Not Connected"
**Causes**:
1. Missing environment variables
2. Invalid API key
3. Pinecone package not installed

**Solutions**:
1. Check `.env` file has `PINECONE_API_KEY` and `PINECONE_INDEX_NAME`
2. Verify API key is valid in Pinecone console
3. Install package: `pip install pinecone-client`

### Issue: MySQL Connection Errors
**Causes**:
1. Database server not running
2. Incorrect connection string
3. Permission issues

**Solutions**:
1. Start MySQL service
2. Verify `DATABASE_URL` in `.env`
3. Check user permissions

### Issue: CSRF Token Errors
**Causes**:
1. Missing CSRF tokens in forms
2. Session expired
3. CSRF protection disabled

**Solutions**:
1. Add `{{ csrf_token() }}` to forms
2. Refresh page and try again
3. Check Flask-WTF configuration

### Issue: Template Not Found
**Causes**:
1. Missing template files
2. Incorrect template paths
3. Template syntax errors

**Solutions**:
1. Create missing templates
2. Check route definitions
3. Validate Jinja2 syntax

## Testing Connection Status

### Manual Testing
1. Navigate to System Information page
2. Click "Refresh All Connections"
3. Check status indicators and error messages

### API Testing
Test individual connections via API endpoints:
```bash
# MongoDB
curl http://localhost:5000/api/connections/mongodb

# MySQL
curl http://localhost:5000/api/connections/mysql

# Pinecone
curl http://localhost:5000/api/connections/pinecone

# OpenAI
curl http://localhost:5000/api/connections/openai
```

## Performance Monitoring

The web console includes:
- Real-time connection latency measurements
- System resource monitoring (CPU, memory, disk)
- Processing statistics and analytics
- User activity tracking

## Security Considerations

1. **Environment Variables**: Never commit `.env` files
2. **CSRF Protection**: All forms include CSRF tokens
3. **Authentication**: All admin functions require login
4. **Role-based Access**: Admin vs user permissions
5. **Input Validation**: All forms validate input data

## Maintenance Tasks

### Daily
- Monitor connection status
- Check system resources
- Review error logs

### Weekly
- Update dependencies
- Review user activity
- Clean old logs

### Monthly
- Security updates
- Performance optimization
- Backup database

## Getting Help

1. Check this troubleshooting guide
2. Review application logs
3. Test individual components
4. Check environment configuration
5. Verify service connectivity

## Log Locations

- **Application Logs**: Check Flask debug output
- **Database Logs**: Check MySQL/SQLite logs
- **System Logs**: Check system event logs
- **Web Console Logs**: Built-in logging system

## Support Contacts

For technical issues:
1. Check documentation first
2. Test with minimal configuration
3. Provide error messages and logs
4. Include environment details
