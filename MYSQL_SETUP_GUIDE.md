# MySQL Database Setup Guide for QA Pipeline

## Prerequisites

1. **XAMPP with MySQL** installed and running
2. **MySQL command line access** or phpMyAdmin
3. **Python virtual environment** activated

## Step 1: Start MySQL Service

Make sure MySQL is running in XAMPP:
1. Open XAMPP Control Panel
2. Start **Apache** and **MySQL** services
3. Verify MySQL is running on port 3306

## Step 2: Create Database Using SQL Script

### Option A: Using MySQL Command Line

1. Open Command Prompt as Administrator
2. Navigate to your project directory:
   ```bash
   cd c:\xampp\htdocs\project-02
   ```

3. Run the SQL script:
   ```bash
   # If MySQL is in your PATH
   mysql -u root -p < setup_mysql_db.sql
   
   # If MySQL is not in PATH, use full path
   c:\xampp\mysql\bin\mysql -u root -p < setup_mysql_db.sql
   ```

4. Enter your MySQL root password when prompted (usually empty for XAMPP)

### Option B: Using phpMyAdmin

1. Open browser and go to: `http://localhost/phpmyadmin`
2. Login with username: `root` (password usually empty for XAMPP)
3. Click on **SQL** tab
4. Copy and paste the contents of `setup_mysql_db.sql`
5. Click **Go** to execute

### Option C: Manual Database Creation

If the script doesn't work, create manually:

1. Open phpMyAdmin or MySQL command line
2. Create database:
   ```sql
   CREATE DATABASE qa_pipeline_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
3. Select the database:
   ```sql
   USE qa_pipeline_db;
   ```
4. Copy and paste the table creation statements from `setup_mysql_db.sql`

## Step 3: Verify Database Setup

### Check Database and Tables
```sql
-- Show databases
SHOW DATABASES;

-- Use the database
USE qa_pipeline_db;

-- Show tables
SHOW TABLES;

-- Check user table
DESCRIBE user;

-- Verify default users
SELECT user_id, username, user_role, is_active FROM user;
```

Expected output:
- Database `qa_pipeline_db` should exist
- Tables: `user`, `processing_log`, `system_metrics`, `user_sessions`
- Two default users: `admin` and `demo`

## Step 4: Update Environment Configuration

The `.env` file has been updated to use your specific MySQL credentials:

```bash
# Individual database parameters
DB_HOST=localhost
DB_NAME=qa_pipeline_db
DB_USER=qa_pipeline_india
DB_PASS=mk@indiagg#n

# Complete connection string
DATABASE_URL=mysql+pymysql://qa_pipeline_india:mk@indiagg#n@localhost:3306/qa_pipeline_db
```

**Note**: The application supports both individual parameters and the complete DATABASE_URL.

## Step 5: Install Required Python Packages

Make sure you have the MySQL connector installed:

```bash
# Activate virtual environment
myenv\Scripts\activate

# Install MySQL connector (should already be in requirements.txt)
pip install pymysql
```

## Step 6: Test the Setup

### Quick Connection Test
```bash
# Test your specific MySQL credentials
python test_mysql_connection.py
```

### Test Database Connection (Alternative)
```bash
python -c "
from web_console.models import db
from app import create_app
app = create_app()
with app.app_context():
    try:
        db.engine.execute(db.text('SELECT 1'))
        print('✅ Database connection successful!')
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
"
```

### Test Web Console
```bash
python run_web_console.py
```

Then open browser to: `http://127.0.0.1:5000`

**Default Login Credentials:**
- Username: `admin`
- Password: `admin123`

## Troubleshooting

### Common Issues

#### 1. "Access denied for user 'root'@'localhost'"
**Solution**: Check your MySQL root password
```bash
# Update .env file with correct password
DATABASE_URL=mysql+pymysql://root:your_actual_password@localhost:3306/qa_pipeline_db
```

#### 2. "Unknown database 'qa_pipeline_db'"
**Solution**: Database wasn't created properly
- Re-run the SQL script
- Or create database manually in phpMyAdmin

#### 3. "No module named 'pymysql'"
**Solution**: Install the MySQL connector
```bash
pip install pymysql
```

#### 4. "Can't connect to MySQL server"
**Solution**: Ensure MySQL is running
- Check XAMPP Control Panel
- Restart MySQL service if needed

#### 5. "Table 'qa_pipeline_db.user' doesn't exist"
**Solution**: Tables weren't created
- Re-run the table creation part of the SQL script
- Check for SQL errors in phpMyAdmin

### Verification Commands

```sql
-- Check if database exists
SHOW DATABASES LIKE 'qa_pipeline_db';

-- Check tables in database
USE qa_pipeline_db;
SHOW TABLES;

-- Check user table structure
DESCRIBE user;

-- Check if default users exist
SELECT COUNT(*) as user_count FROM user;

-- Check admin user specifically
SELECT username, user_role, is_active FROM user WHERE username = 'admin';
```

## Security Notes

1. **Change default passwords** in production:
   ```sql
   -- Update admin password (use bcrypt hash)
   UPDATE user SET password_hash = 'new_bcrypt_hash' WHERE username = 'admin';
   ```

2. **Create dedicated MySQL user** for production:
   ```sql
   CREATE USER 'qa_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON qa_pipeline_db.* TO 'qa_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Update .env file** with new credentials:
   ```bash
   DATABASE_URL=mysql+pymysql://qa_user:secure_password@localhost:3306/qa_pipeline_db
   ```

## Next Steps

After successful database setup:

1. **Test the web console**: `python run_web_console.py`
2. **Login with admin credentials**
3. **Check system status** in the dashboard
4. **Create additional users** if needed
5. **Configure other services** (MongoDB, OpenAI, Pinecone)

## Support

If you encounter issues:
1. Check XAMPP MySQL logs: `c:\xampp\mysql\data\*.err`
2. Check application logs
3. Verify all services are running in XAMPP
4. Test database connection independently
