#!/usr/bin/env python3
"""
Test script to verify all fixes are working properly
Run this after setting up the MySQL database
"""

import os
import sys
import traceback
from datetime import datetime

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    try:
        from flask import Flask
        from web_console.models import db, User, ProcessingLog, SystemMetrics
        from web_console.auth import auth_bp
        from web_console.dashboard import dashboard_bp
        from web_console.api import api_bp
        from web_console.admin import admin_bp
        from app import create_app
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection"""
    print("\n🔍 Testing database connection...")
    try:
        from app import create_app
        from web_console.models import db
        
        app = create_app()
        with app.app_context():
            # Test basic connection
            result = db.engine.execute(db.text('SELECT 1 as test')).fetchone()
            if result and result[0] == 1:
                print("✅ Database connection successful")
                return True
            else:
                print("❌ Database connection test failed")
                return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        print("💡 Make sure MySQL is running and qa_pipeline_db database exists")
        return False

def test_database_tables():
    """Test that all required tables exist"""
    print("\n🔍 Testing database tables...")
    try:
        from app import create_app
        from web_console.models import db, User, ProcessingLog, SystemMetrics
        
        app = create_app()
        with app.app_context():
            # Test each table
            tables_to_test = [
                ('user', User),
                ('processing_log', ProcessingLog),
                ('system_metrics', SystemMetrics)
            ]
            
            for table_name, model in tables_to_test:
                try:
                    count = model.query.count()
                    print(f"✅ Table '{table_name}': {count} records")
                except Exception as e:
                    print(f"❌ Table '{table_name}' error: {e}")
                    return False
            
            return True
    except Exception as e:
        print(f"❌ Database tables error: {e}")
        return False

def test_admin_user():
    """Test that admin user exists"""
    print("\n🔍 Testing admin user...")
    try:
        from app import create_app
        from web_console.models import db, User
        
        app = create_app()
        with app.app_context():
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print(f"✅ Admin user exists: {admin_user.username} (Role: {admin_user.user_role})")
                
                # Test password verification
                if admin_user.check_password('admin123'):
                    print("✅ Admin password verification successful")
                    return True
                else:
                    print("❌ Admin password verification failed")
                    return False
            else:
                print("❌ Admin user not found")
                return False
    except Exception as e:
        print(f"❌ Admin user test error: {e}")
        return False

def test_templates():
    """Test that all required templates exist"""
    print("\n🔍 Testing template files...")
    
    required_templates = [
        'templates/base.html',
        'templates/auth/login.html',
        'templates/auth/register.html',
        'templates/auth/change_password.html',
        'templates/dashboard/index.html',
        'templates/dashboard/logs.html',
        'templates/dashboard/process.html',
        'templates/dashboard/system_info.html',
        'templates/admin/index.html',
        'templates/admin/users.html',
        'templates/admin/system.html',
        'templates/admin/edit_user.html',
        'templates/admin/analytics.html',
        'templates/errors/403.html',
        'templates/errors/404.html',
        'templates/errors/500.html'
    ]
    
    missing_templates = []
    for template in required_templates:
        if os.path.exists(template):
            print(f"✅ {template}")
        else:
            print(f"❌ {template} - MISSING")
            missing_templates.append(template)
    
    if missing_templates:
        print(f"\n❌ Missing {len(missing_templates)} template(s)")
        return False
    else:
        print(f"\n✅ All {len(required_templates)} templates found")
        return True

def test_csrf_tokens():
    """Test that CSRF tokens are properly configured"""
    print("\n🔍 Testing CSRF configuration...")
    try:
        from app import create_app
        
        app = create_app()
        csrf_enabled = app.config.get('WTF_CSRF_ENABLED', False)
        
        if csrf_enabled:
            print("✅ CSRF protection enabled")
            return True
        else:
            print("❌ CSRF protection not enabled")
            return False
    except Exception as e:
        print(f"❌ CSRF test error: {e}")
        return False

def test_environment_variables():
    """Test that required environment variables are set"""
    print("\n🔍 Testing environment variables...")

    # Check database configuration
    database_url = os.getenv('DATABASE_URL')
    db_individual_params = all([
        os.getenv('DB_HOST'),
        os.getenv('DB_NAME'),
        os.getenv('DB_USER'),
        os.getenv('DB_PASS')
    ])

    if database_url:
        print("✅ DATABASE_URL: [SET]")
    elif db_individual_params:
        print("✅ Individual DB parameters: [SET]")
        print(f"✅ DB_HOST: {os.getenv('DB_HOST')}")
        print(f"✅ DB_NAME: {os.getenv('DB_NAME')}")
        print(f"✅ DB_USER: {os.getenv('DB_USER')}")
        print("✅ DB_PASS: [SET]")
    else:
        print("❌ Database configuration: [NOT SET]")
        print("💡 Either set DATABASE_URL or all individual DB parameters")

    required_vars = [
        'SECRET_KEY',
        'MONGO_URI',
        'OPENAI_API_KEY',
        'PINECONE_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Don't print sensitive values
            if 'KEY' in var or 'URI' in var:
                print(f"✅ {var}: [SET]")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: [NOT SET]")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ Missing {len(missing_vars)} environment variable(s)")
        print("💡 Check your .env file")
        return False
    else:
        print(f"\n✅ All {len(required_vars)} environment variables set")
        return True

def test_web_app_creation():
    """Test that the Flask app can be created"""
    print("\n🔍 Testing Flask app creation...")
    try:
        from app import create_app
        
        app = create_app()
        if app:
            print("✅ Flask app created successfully")
            print(f"✅ App name: {app.name}")
            print(f"✅ Debug mode: {app.debug}")
            return True
        else:
            print("❌ Flask app creation failed")
            return False
    except Exception as e:
        print(f"❌ Flask app creation error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 QA Pipeline - Testing All Fixes")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    tests = [
        ("Imports", test_imports),
        ("Environment Variables", test_environment_variables),
        ("Templates", test_templates),
        ("Flask App Creation", test_web_app_creation),
        ("Database Connection", test_database_connection),
        ("Database Tables", test_database_tables),
        ("Admin User", test_admin_user),
        ("CSRF Configuration", test_csrf_tokens)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your QA Pipeline is ready to use.")
        print("\n🚀 Next steps:")
        print("1. Run: python run_web_console.py")
        print("2. Open: http://127.0.0.1:5000")
        print("3. Login with: admin / admin123")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        print("\n💡 Common solutions:")
        print("- Make sure MySQL is running in XAMPP")
        print("- Run the setup_mysql_db.sql script")
        print("- Check your .env file configuration")
        print("- Ensure all dependencies are installed: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
