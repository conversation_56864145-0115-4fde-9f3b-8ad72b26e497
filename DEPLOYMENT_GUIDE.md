# QA Pipeline Web Console - Deployment Guide

## Quick Start

### 1. Prerequisites Check
- ✅ Python 3.8+ installed
- ✅ Virtual environment activated (`myenv\Scripts\activate`)
- ✅ All dependencies installed (`pip install -r requirements.txt`)
- ✅ Environment variables configured (`.env` file)

### 2. Database Setup
```bash
# Test the web console
python test_web_console.py

# Or initialize manually
python init_db.py init
```

### 3. Launch Web Console
```bash
# Simple launch
python test_web_console.py

# Or use the launcher
python run_web_console.py

# Or run Flask directly
python app.py
```

### 4. Access Web Interface
- **URL**: http://127.0.0.1:5000
- **Admin Username**: admin
- **Admin Password**: admin123

## Production Deployment

### 1. Environment Configuration
Update `.env` for production:
```bash
# Production Database (MySQL)
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/qa_pipeline_db

# Security
SECRET_KEY=your-very-secure-secret-key-here
FLASK_ENV=production
FLASK_DEBUG=False

# Server Configuration
WEB_HOST=0.0.0.0
WEB_PORT=5000

# Change default admin credentials
ADMIN_USERNAME=your_admin_username
ADMIN_PASSWORD=your_secure_admin_password
```

### 2. Database Setup (MySQL)
```sql
-- Create database
CREATE DATABASE qa_pipeline_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER 'qa_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON qa_pipeline_db.* TO 'qa_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Production Server (Gunicorn)
```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Or with more options
gunicorn -w 4 -b 0.0.0.0:5000 --timeout 120 --keep-alive 2 app:app
```

### 4. Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/your/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Features Overview

### 🔐 Authentication & Security
- Secure login/logout with session management
- Role-based access control (Admin/User)
- Password strength validation
- CSRF protection
- Secure password hashing with bcrypt

### 📊 Dashboard & Monitoring
- Real-time system status indicators
- Connection health checks (MongoDB, MySQL, Pinecone, OpenAI)
- Processing statistics and analytics
- User usage tracking and limits
- System resource monitoring

### 🎯 Transcript Processing
- Web-based transcript processing interface
- Real-time processing status updates
- Usage limit enforcement
- Processing history and logs
- Result viewing and analysis

### 👥 User Management (Admin)
- User creation and management
- Role assignment and permissions
- Usage limit configuration
- User activity monitoring
- Account activation/deactivation

### 📈 Analytics & Reporting
- Processing performance metrics
- User activity analytics
- System health trends
- Data export capabilities
- Custom reporting dashboards

### 🔧 API Endpoints
- RESTful API for system monitoring
- Health check endpoints
- Connection status APIs
- Processing statistics
- User management APIs

## File Structure

```
project-02/
├── app.py                     # Main Flask application
├── run_web_console.py         # Web console launcher
├── test_web_console.py        # Test script
├── init_db.py                 # Database initialization
├── main.py                    # Original CLI pipeline
├── utils.py                   # Database utilities
├── qa_assessment.py           # OpenAI integration
├── requirements.txt           # Dependencies
├── .env                       # Environment variables
├── .env.example               # Environment template
├── .gitignore                 # Git ignore rules
├── README.md                  # Main documentation
├── WEB_CONSOLE_README.md      # Web console docs
├── DEPLOYMENT_GUIDE.md        # This file
├── web_console/               # Web console package
│   ├── __init__.py
│   ├── models.py              # Database models
│   ├── auth.py                # Authentication
│   ├── dashboard.py           # Dashboard views
│   ├── api.py                 # API endpoints
│   └── admin.py               # Admin functions
├── templates/                 # HTML templates
│   ├── base.html              # Base template
│   ├── auth/                  # Auth templates
│   ├── dashboard/             # Dashboard templates
│   ├── admin/                 # Admin templates
│   └── errors/                # Error pages
└── myenv/                     # Virtual environment
```

## Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Ensure virtual environment is activated
myenv\Scripts\activate

# Install missing dependencies
pip install -r requirements.txt
```

#### 2. Database Connection Issues
```bash
# Test database connection
python init_db.py check

# Reset database if needed
python init_db.py reset
```

#### 3. Permission Errors
- Check user role assignments
- Verify account is active
- Check usage limits

#### 4. Port Already in Use
```bash
# Change port in .env file
WEB_PORT=5001

# Or kill existing process
netstat -ano | findstr :5000
taskkill /PID <process_id> /F
```

### Performance Optimization

#### 1. Database Optimization
- Use connection pooling
- Add database indexes
- Regular maintenance

#### 2. Application Optimization
- Enable caching
- Optimize queries
- Use CDN for static files

#### 3. Server Optimization
- Use production WSGI server
- Configure reverse proxy
- Enable gzip compression

## Security Checklist

### Production Security
- [ ] Change default admin credentials
- [ ] Use strong secret key
- [ ] Enable HTTPS
- [ ] Configure firewall
- [ ] Regular security updates
- [ ] Database access controls
- [ ] API rate limiting
- [ ] Log monitoring
- [ ] Backup strategy
- [ ] Error handling

### Development Security
- [ ] Never commit .env file
- [ ] Use environment variables
- [ ] Validate all inputs
- [ ] Sanitize outputs
- [ ] Regular dependency updates

## Maintenance

### Regular Tasks
- Monitor system resources
- Check application logs
- Update dependencies
- Backup database
- Review user activity
- Clean old logs

### Updates
```bash
# Update dependencies
pip install -r requirements.txt --upgrade

# Database migrations (if needed)
flask db upgrade

# Restart application
```

## Support

For issues and questions:
1. Check this deployment guide
2. Review application logs
3. Test individual components
4. Check environment configuration
5. Verify database connectivity

## License

This project follows the same licensing terms as the main QA Pipeline project.
