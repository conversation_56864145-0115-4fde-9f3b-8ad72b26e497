-- QA Pipeline Database Setup Script for Localhost MySQL
-- Run this script with: mysql -u root -p < setup_mysql_db.sql

-- Create database
CREATE DATABASE IF NOT EXISTS qa_pipeline_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user with your specific credentials
CREATE USER IF NOT EXISTS 'qa_pipeline_india'@'localhost' IDENTIFIED BY 'mk@indiagg#n';
GRANT ALL PRIVILEGES ON qa_pipeline_db.* TO 'qa_pipeline_india'@'localhost';
FLUSH PRIVILEGES;

-- Use the database
USE qa_pipeline_db;

-- Create user table
CREATE TABLE IF NOT EXISTS user (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(80) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_role ENUM('admin', 'user') DEFAULT 'user',
    daily_limit INT DEFAULT 100,
    monthly_limit INT DEFAULT 3000,
    daily_usage INT DEFAULT 0,
    monthly_usage INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    INDEX idx_username (username),
    INDEX idx_user_role (user_role),
    INDEX idx_is_active (is_active)
);

-- Create processing_log table
CREATE TABLE IF NOT EXISTS processing_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    call_id VARCHAR(255),
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    processing_time DECIMAL(10, 3),
    error_message TEXT,
    input_size INT,
    output_size INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_call_id (call_id)
);

-- Create system_metrics table
CREATE TABLE IF NOT EXISTS system_metrics (
    metric_id INT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15, 6),
    metric_unit VARCHAR(50),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at)
);

-- Create user_sessions table for tracking login sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    session_token VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at)
);

-- Insert default admin user (password: admin123)
-- Note: This is a bcrypt hash of 'admin123'
INSERT IGNORE INTO user (username, password_hash, user_role, daily_limit, monthly_limit) 
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', 'admin', 1000, 30000);

-- Insert sample demo user (password: demo123)
-- Note: This is a bcrypt hash of 'demo123'
INSERT IGNORE INTO user (username, password_hash, user_role, daily_limit, monthly_limit) 
VALUES ('demo', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 100, 3000);

-- Insert initial system metrics
INSERT IGNORE INTO system_metrics (metric_name, metric_value, metric_unit) VALUES
('total_users', 2, 'count'),
('total_processing_logs', 0, 'count'),
('system_uptime', 0, 'seconds'),
('database_size', 0, 'MB');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_created_at ON user(created_at);
CREATE INDEX IF NOT EXISTS idx_processing_log_created_at ON processing_log(created_at);
CREATE INDEX IF NOT EXISTS idx_system_metrics_recorded_at ON system_metrics(recorded_at);

-- Show created tables
SHOW TABLES;

-- Show user table structure
DESCRIBE user;

-- Display created users
SELECT user_id, username, user_role, daily_limit, monthly_limit, is_active, created_at FROM user;

-- Success message
SELECT 'Database qa_pipeline_db created successfully!' AS message;
